"""
代码修复Agent的状态定义
"""

from typing import List, Dict, Any, Annotated, Optional
from typing_extensions import TypedDict
from pydantic import BaseModel, Field
from langgraph.graph import add_messages
from langchain_core.messages import BaseMessage


class CodeFix(BaseModel):
    """单个代码修复"""
    line: int = Field(description="需要修复的行号")
    before: Optional[str] = Field(default="", description="修复前的代码内容")
    after: str = Field(description="修复后的代码内容")
    insert_after: Optional[str] = Field(default=None, description="在指定行后插入的新代码")
    operation: str = Field(default="replace", description="操作类型: replace, insert, delete")


class CodeFixSuggestions(BaseModel):
    """代码修复建议列表"""
    fixes: List[CodeFix] = Field(description="修复建议列表")
    summary: str = Field(description="修复总结", default="")


class CodeFixState(TypedDict):
    """代码修复Agent的状态"""
    # 输入信息
    file_path: str
    issues: List[Dict[str, Any]]
    
    # 处理过程中的数据
    original_code: str
    rule_details: Dict[str, Any]
    fix_suggestions: List[Dict[str, Any]]
    
    # 消息历史
    messages: Annotated[List[BaseMessage], add_messages]
    
    # 输出结果
    result: Optional[Dict[str, Any]]
    error: Optional[str]
