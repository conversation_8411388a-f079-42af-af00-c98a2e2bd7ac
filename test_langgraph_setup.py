#!/usr/bin/env python3
"""
测试LangGraph设置
"""

import os
import sys
from dotenv import load_dotenv

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        from src.code_fix_agent.state import CodeFixState, CodeFix, CodeFixSuggestions
        print("✅ 状态模型导入成功")
    except Exception as e:
        print(f"❌ 状态模型导入失败: {e}")
        return False
    
    try:
        from src.code_fix_agent.tools import tools
        print(f"✅ 工具导入成功，共 {len(tools)} 个工具")
    except Exception as e:
        print(f"❌ 工具导入失败: {e}")
        return False
    
    try:
        from src.code_fix_agent.nodes import analyze_issues, generate_fixes, apply_fixes
        print("✅ 节点函数导入成功")
    except Exception as e:
        print(f"❌ 节点函数导入失败: {e}")
        return False
    
    try:
        from src.code_fix_agent.graph import graph
        print("✅ 图导入成功")
        print(f"📊 图节点: {list(graph.nodes.keys())}")
    except Exception as e:
        print(f"❌ 图导入失败: {e}")
        return False
    
    return True

def test_api():
    """测试API"""
    print("\n🔌 测试API...")
    
    try:
        from src.code_fix_agent.api import FixRequest, process_fix_request_sync
        
        # 创建测试请求
        request = FixRequest(
            file_path="test_file.java",
            issues=[
                {
                    "rule": "test:rule",
                    "line": 1,
                    "message": "Test issue"
                }
            ]
        )
        
        print("✅ API模型创建成功")
        print(f"📋 测试请求: {request.file_path}, {len(request.issues)} 个问题")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_cli():
    """测试CLI"""
    print("\n💻 测试CLI...")
    
    try:
        from src.code_fix_agent.cli import load_issues
        
        # 测试加载issues（如果文件存在）
        if os.path.exists("data/issues.json"):
            issues_data = load_issues()
            print(f"✅ CLI功能正常，加载了 {len(issues_data)} 个文件的issues")
        else:
            print("⚠️ data/issues.json 不存在，跳过CLI测试")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("\n🌍 测试环境配置...")
    
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    if api_key:
        print(f"✅ OPENAI_API_KEY: {api_key[:10]}...")
    else:
        print("⚠️ OPENAI_API_KEY 未设置")
    
    if base_url:
        print(f"✅ OPENAI_BASE_URL: {base_url}")
    else:
        print("ℹ️ OPENAI_BASE_URL 使用默认值")
    
    return True

def test_langgraph_config():
    """测试LangGraph配置"""
    print("\n⚙️ 测试LangGraph配置...")
    
    if os.path.exists("langgraph.json"):
        import json
        with open("langgraph.json", "r") as f:
            config = json.load(f)
        
        print("✅ langgraph.json 存在")
        print(f"📊 配置的图: {list(config.get('graphs', {}).keys())}")
        
        # 检查图路径是否正确
        for graph_name, graph_path in config.get('graphs', {}).items():
            module_path, attr = graph_path.split(':')
            print(f"📍 {graph_name}: {module_path}:{attr}")
        
        return True
    else:
        print("❌ langgraph.json 不存在")
        return False

def main():
    """主测试函数"""
    print("🚀 LangGraph设置测试")
    print("=" * 50)
    
    tests = [
        ("环境配置", test_environment),
        ("LangGraph配置", test_langgraph_config),
        ("导入测试", test_imports),
        ("API测试", test_api),
        ("CLI测试", test_cli)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以使用 'langgraph dev' 启动。")
        print("\n📋 下一步:")
        print("1. 运行 'langgraph dev' 启动开发服务器")
        print("2. 访问Web界面查看工作流")
        print("3. 使用API或CLI进行代码修复")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
