"""
代码修复Agent的API端点
"""

import json
from typing import Dict, Any, List
# from fastapi import HTTPException  # Optional dependency
from pydantic import BaseModel

from .graph import graph
from .state import CodeFixState


class FixRequest(BaseModel):
    """修复请求模型"""
    file_path: str
    issues: List[Dict[str, Any]]


class FixResponse(BaseModel):
    """修复响应模型"""
    status: str
    result: Dict[str, Any]
    error: str = None


async def process_fix_request(request: FixRequest) -> FixResponse:
    """
    处理代码修复请求
    
    Args:
        request: 修复请求，包含文件路径和问题列表
        
    Returns:
        修复结果
    """
    try:
        # 构建初始状态
        initial_state: CodeFixState = {
            "file_path": request.file_path,
            "issues": request.issues,
            "original_code": "",
            "rule_details": {},
            "fix_suggestions": [],
            "messages": [],
            "result": None,
            "error": None
        }
        
        # 执行工作流
        result = await graph.ainvoke(initial_state)
        
        if result.get("error"):
            return FixResponse(
                status="error",
                result={},
                error=result["error"]
            )
        
        return FixResponse(
            status="success",
            result=result.get("result", {}),
            error=None
        )
        
    except Exception as e:
        return FixResponse(
            status="error",
            result={},
            error=str(e)
        )


def process_fix_request_sync(request: FixRequest) -> FixResponse:
    """
    同步处理代码修复请求
    
    Args:
        request: 修复请求，包含文件路径和问题列表
        
    Returns:
        修复结果
    """
    try:
        # 构建初始状态
        initial_state: CodeFixState = {
            "file_path": request.file_path,
            "issues": request.issues,
            "original_code": "",
            "rule_details": {},
            "fix_suggestions": [],
            "messages": [],
            "result": None,
            "error": None
        }
        
        # 执行工作流
        result = graph.invoke(initial_state)
        
        if result.get("error"):
            return FixResponse(
                status="error",
                result={},
                error=result["error"]
            )
        
        return FixResponse(
            status="success",
            result=result.get("result", {}),
            error=None
        )
        
    except Exception as e:
        return FixResponse(
            status="error",
            result={},
            error=str(e)
        )


def process_batch_fixes(file_issues_map: Dict[str, List[Dict[str, Any]]]) -> Dict[str, FixResponse]:
    """
    批量处理代码修复请求
    
    Args:
        file_issues_map: 文件路径到问题列表的映射
        
    Returns:
        每个文件的修复结果
    """
    results = {}
    
    for file_path, issues in file_issues_map.items():
        print(f"🔧 处理文件: {file_path}")
        
        request = FixRequest(file_path=file_path, issues=issues)
        result = process_fix_request_sync(request)
        results[file_path] = result
        
        if result.status == "success":
            print(f"✅ {file_path} 修复成功")
        else:
            print(f"❌ {file_path} 修复失败: {result.error}")
    
    return results
