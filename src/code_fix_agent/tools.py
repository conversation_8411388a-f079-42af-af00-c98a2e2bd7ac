"""
代码修复Agent的工具定义
"""

import json
import os
from typing import Dict, Any
from pydantic import BaseModel, Field
from langchain_core.tools import tool


class RuleQueryInput(BaseModel):
    """规则查询工具输入"""
    rule_key: str = Field(description="要查询的规则键名")


class FileReadInput(BaseModel):
    """文件读取工具输入"""
    file_path: str = Field(description="要读取的文件路径")


class FileWriteInput(BaseModel):
    """文件写入工具输入"""
    file_path: str = Field(description="要写入的文件路径")
    content: str = Field(description="要写入的内容")


@tool("get_rule_details", args_schema=RuleQueryInput)
def get_rule_details(rule_key: str) -> str:
    """
    获取代码质量规则的详细信息
    
    Args:
        rule_key: 规则键名，如 'squid:S2111'
        
    Returns:
        规则的详细信息，包括描述、示例等
    """
    try:
        with open('data/rules_origin.json', 'r', encoding='utf-8') as f:
            rules_data = json.load(f)
        
        if rule_key in rules_data:
            rule_info = rules_data[rule_key]
            return json.dumps(rule_info, ensure_ascii=False, indent=2)
        else:
            return f"规则 {rule_key} 未找到"
            
    except Exception as e:
        return f"读取规则信息失败: {e}"


@tool("read_source_file", args_schema=FileReadInput)
def read_source_file(file_path: str) -> str:
    """
    读取源代码文件内容
    
    Args:
        file_path: 文件路径（相对路径）
        
    Returns:
        文件内容
    """
    from config import get_full_path
    
    try:
        full_path = get_full_path(file_path)
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return content
        
    except Exception as e:
        error_msg = f"CRITICAL ERROR: 无法读取文件 {file_path}: {e}"
        print(error_msg)
        raise RuntimeError(error_msg)


@tool("write_source_file", args_schema=FileWriteInput)
def write_source_file(file_path: str, content: str) -> str:
    """
    写入修复后的源代码文件
    
    Args:
        file_path: 文件路径（相对路径）
        content: 修复后的文件内容
        
    Returns:
        操作结果
    """
    from config import get_full_path
    import json
    from datetime import datetime
    
    try:
        full_path = get_full_path(file_path)
        
        # 写入文件
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 记录到日志
        log_entry = {
            "file": file_path,
            "action": "file_updated",
            "timestamp": datetime.now().isoformat()
        }
        
        with open("autofix_argument.log", "a", encoding='utf-8') as log_file:
            log_file.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
        
        return f"文件 {file_path} 修复完成"
        
    except Exception as e:
        error_msg = f"CRITICAL ERROR: 无法写入文件 {file_path}: {e}"
        print(error_msg)
        raise RuntimeError(error_msg)


# 工具列表
tools = [get_rule_details, read_source_file, write_source_file]
