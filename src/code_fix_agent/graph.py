"""
代码修复Agent的主图定义
"""

from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode

from .state import CodeFixState
from .tools import tools
from .nodes import analyze_issues, should_use_tools, generate_fixes, apply_fixes


def create_graph():
    """创建代码修复工作流图"""
    
    # 创建工具节点
    tool_node = ToolNode(tools)
    
    # 创建状态图
    graph_builder = StateGraph(CodeFixState)
    
    # 添加节点
    graph_builder.add_node("analyze_issues", analyze_issues)
    graph_builder.add_node("generate_fixes", generate_fixes)
    graph_builder.add_node("apply_fixes", apply_fixes)
    graph_builder.add_node("tools", tool_node)
    
    # 添加边
    graph_builder.add_edge(START, "analyze_issues")
    graph_builder.add_conditional_edges(
        "analyze_issues",
        should_use_tools,
        {"tools": "tools", "continue": "generate_fixes", "generate_fixes": "generate_fixes"}
    )
    graph_builder.add_edge("tools", "generate_fixes")
    graph_builder.add_edge("generate_fixes", "apply_fixes")
    graph_builder.add_edge("apply_fixes", END)
    
    return graph_builder.compile()


# 创建图实例
graph = create_graph()
