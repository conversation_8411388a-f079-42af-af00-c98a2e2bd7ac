"""
代码修复Agent的CLI入口
"""

import json
import sys
from typing import Dict, Any, List

from .api import FixRequest, process_fix_request_sync, process_batch_fixes


def load_issues(issues_file: str = "data/issues.json") -> Dict[str, List[Dict[str, Any]]]:
    """加载issues数据"""
    try:
        with open(issues_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载issues文件失败: {e}")
        sys.exit(1)


def process_single_file(file_path: str, issues_file: str = "data/issues.json"):
    """处理单个文件的issues"""
    print(f"=== 处理单个文件: {file_path} ===")
    
    # 加载issues数据
    issues_data = load_issues(issues_file)
    
    if file_path not in issues_data:
        print(f"❌ 文件 {file_path} 在issues数据中未找到")
        print("可用文件:")
        for available_file in list(issues_data.keys())[:10]:
            print(f"  - {available_file}")
        return None
    
    file_issues = issues_data[file_path]
    print(f"📋 发现 {len(file_issues)} 个问题")
    
    # 显示问题概要
    for j, issue in enumerate(file_issues, 1):
        rule = issue.get("rule", "Unknown")
        line = issue.get("line", "Unknown")
        message = issue.get("message", "No message")[:100]
        print(f"  {j}. {rule} (行 {line}): {message}...")
    
    # 处理问题
    print(f"\n🔧 开始处理...")
    request = FixRequest(file_path=file_path, issues=file_issues)
    result = process_fix_request_sync(request)
    
    # 显示结果
    if result.status == "success":
        fix_result = result.result
        applied_fixes = fix_result.get("applied_fixes", 0)
        total_fixes = fix_result.get("total_fixes", 0)
        
        print(f"✅ 处理完成!")
        print(f"📊 修复统计: {applied_fixes}/{total_fixes} 个修复已应用")
    else:
        print(f"❌ 处理失败: {result.error}")
    
    return result


def process_all_files(issues_file: str = "data/issues.json"):
    """处理所有文件的issues"""
    print("=== 批量处理所有文件 ===")
    
    # 加载issues数据
    issues_data = load_issues(issues_file)
    
    print(f"📊 发现 {len(issues_data)} 个文件需要处理")
    
    # 批量处理
    results = process_batch_fixes(issues_data)
    
    # 统计结果
    success_count = 0
    total_applied_fixes = 0
    
    for file_path, result in results.items():
        if result.status == "success":
            success_count += 1
            applied_fixes = result.result.get("applied_fixes", 0)
            total_applied_fixes += applied_fixes
    
    print(f"\n📊 批量处理完成!")
    print(f"✅ 成功处理: {success_count}/{len(issues_data)} 个文件")
    print(f"🔧 总修复数: {total_applied_fixes} 个")
    
    return results


def main():
    """CLI主入口"""
    import os
    from dotenv import load_dotenv
    
    # 加载环境变量
    load_dotenv()
    
    print("🚀 代码质量Issue自动修复Agent")
    print("基于LangGraph实现")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        process_single_file(file_path)
    else:
        process_all_files()


if __name__ == "__main__":
    main()
